variable "env" {}
variable "tenant" {}

variable "dynamodb_table_name" {}

variable "backend_bucket_name" {}

variable "aws_region" {}
variable "aws_account_id" {}

variable "aws_azs" {
  type = list(string)
}

variable "web_domain" {}

variable "use_self_signed_certificate" {
  type = bool
}

variable "user_pool_arn_enabled" {
  type = bool
}

variable "jazz_enabled" {
  type = bool
  default = false
}

variable "chatbot_adicional_secrets_map" {
  type = map(string)
}

variable "chatbot_adicional_tenants" {
  type = list(string)
  default = []
}