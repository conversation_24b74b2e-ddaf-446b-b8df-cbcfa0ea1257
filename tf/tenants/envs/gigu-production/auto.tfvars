env = "production"
tenant = "gigu"
web_domain = "via1.app"
backend_bucket_name = "tf-gigu-backend-infrastructure-production"
dynamodb_table_name = "tf-gigu-backend-infrastructure-lock-table"

aws_region = "us-east-1"
aws_account_id = "************"
aws_azs = ["us-east-1a", "us-east-1b"]

user_pool_arn_enabled = false
use_self_signed_certificate = false


chatbot_adicitional_tenants = [
  "motorola"
]

chatbot_adicional_secrets_map = {
  "TENANTS_MOTOROLA_COMMUNICATION_CENTRE_INTEGRATION_BLIP_AUTH" = "bill-payment-api/blip-password"
  "TENANTS_MOTOROLA_INTEGRATIONS_WHATSAPP_API_TOKEN"            = "friday/waba_token"
  "TENANTS_GIGU_COMMUNICATION_CENTRE_INTEGRATION_BLIP_AUTH" = "bill-payment-api/blip-password"
  "TENANTS_GIGU_INTEGRATIONS_WHATSAPP_API_TOKEN"            = "friday/waba_token"
}

gitlab_env    = "gigu-production"

#export TF_VAR_env="prod"
#export TF_VAR_tenant="motorola"
#export TF_VAR_web_domain="via1.app"
#export TF_VAR_backend_bucket_name="tf-motorola-backend-infrastructure-prod"
#export TF_VAR_dynamodb_table_name="tf-motorola-backend-infrastructure-lock-table"
#export TF_VAR_aws_region="us-east-1"
#export TF_VAR_aws_account_id="************"
#export TF_VAR_aws_azs="[\"us-east-1a\", \"us-east-1b\"]"
#export TF_VAR_user_pool_arn_enabled=false
#export TF_VAR_use_self_signed_certificate=true
#export TF_VAR_gitlab_env="motorola-prod"