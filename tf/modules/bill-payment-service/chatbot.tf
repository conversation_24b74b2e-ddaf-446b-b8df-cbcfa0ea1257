module "ai_chatbot" {
  source = "../../modules/ai-chatbot"

  aws_ecs_bill_payment_cluster = var.aws_ecs_bill_payment_cluster
  account_id                   = var.account_id
  aws_region                   = "us-east-1"
  environment                  = var.environment
  app_environments = replace(var.app_environments, ",friday", "") + length(var.chatbot_adicional_tenants) > 0 ? ",${join(",",var.chatbot_adicional_tenants)}" : ""
  task_definition              = local.chatbot_task_definition
  private_subnets              = var.aws_private_subnet_id
  public_subnets               = var.aws_public_subnet_id
  vpc_id                       = var.aws_vpc_id
  certificate_arn              = var.certificate_arn
  ecs_sqs_list_policy_resource = local.ecs_sqs_list_policy_resource
  shedlock_table_arn           = aws_dynamodb_table.server_lock_table.arn
  billpayment_table_arn        = aws_dynamodb_table.user_table.arn
  blip_password_secret_arn     = aws_secretsmanager_secret.blip_password.arn
  open_ai_key_secret_arn       = aws_secretsmanager_secret.openai_key.arn
  waba_token_arn               = aws_secretsmanager_secret.waba_token.arn
  datadog_secret_arn           = var.datadog_key_arn
  tags                         = var.tags
  chatbot_adicional_secrets_map = var.chatbot_adicional_secrets_map
}